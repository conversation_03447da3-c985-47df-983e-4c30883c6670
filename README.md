# Tasks Management System

A comprehensive Spring Boot application for managing tasks with MySQL database support.

## Features

- ✅ **CRUD Operations** for tasks
- ✅ **Task Status Management** (Pending, In Progress, Completed, Cancelled, On Hold)
- ✅ **Priority Levels** (Low, Medium, High, Urgent, Critical)
- ✅ **Task Assignment** to users
- ✅ **Due Date Tracking**
- ✅ **Search and Filter** capabilities
- ✅ **Task Statistics** and reporting
- ✅ **RESTful API** with proper HTTP status codes
- ✅ **Input Validation** with detailed error messages
- ✅ **MySQL Database** integration
- ✅ **H2 Database** support for testing

## Technology Stack

- **Java 17+**
- **Spring Boot 3.5.5**
- **Spring Data JPA**
- **MySQL 8.0+**
- **H2 Database** (for testing)
- **Lombok** (for reducing boilerplate code)
- **Maven** (for dependency management)

## Prerequisites

1. **Java 17 or higher**
2. **Maven 3.6+**
3. **MySQL 8.0+** (running on localhost:3306)

## Database Setup

### Option 1: MySQL (Recommended for Production)

1. **Install MySQL** if not already installed
2. **Start MySQL service**
3. **Run the setup script**:
   ```bash
   mysql -u root -p < setup-mysql.sql
   ```
4. **Update credentials** in `application.properties` if needed

### Option 2: H2 In-Memory (For Testing)

1. **Switch to test profile** by commenting MySQL config and uncommenting H2 config in `application.properties`
2. **Access H2 Console** at `http://localhost:8080/h2-console`

## Running the Application

### Development Mode
```bash
# Using Maven
mvn spring-boot:run

# Using specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Using JAR
mvn clean package
java -jar target/tasks-0.0.1-SNAPSHOT.jar
```

### Test Mode
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=test
```

## API Endpoints

### Base URL: `http://localhost:8080`

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Welcome message and API info |
| GET | `/health` | Application health check |
| GET | `/api/tasks` | Get all tasks |
| POST | `/api/tasks` | Create a new task |
| GET | `/api/tasks/{id}` | Get task by ID |
| PUT | `/api/tasks/{id}` | Update task |
| DELETE | `/api/tasks/{id}` | Delete task |
| GET | `/api/tasks/status/{status}` | Get tasks by status |
| GET | `/api/tasks/priority/{priority}` | Get tasks by priority |
| GET | `/api/tasks/assigned/{username}` | Get tasks assigned to user |
| GET | `/api/tasks/search?title={title}` | Search tasks by title |
| GET | `/api/tasks/overdue` | Get overdue tasks |
| GET | `/api/tasks/high-priority-pending` | Get high priority pending tasks |
| PATCH | `/api/tasks/{id}/complete` | Mark task as completed |
| PATCH | `/api/tasks/{id}/in-progress` | Mark task as in progress |
| GET | `/api/tasks/statistics` | Get task statistics |

## API Usage Examples

### Create a Task
```bash
curl -X POST http://localhost:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Setup Development Environment",
    "description": "Install and configure all necessary development tools",
    "priority": "HIGH",
    "createdBy": "john_doe",
    "assignedTo": "jane_smith",
    "dueDate": "2024-12-31T23:59:59"
  }'
```

### Get All Tasks
```bash
curl http://localhost:8080/api/tasks
```

### Update Task Status
```bash
curl -X PATCH http://localhost:8080/api/tasks/1/complete
```

### Search Tasks
```bash
curl "http://localhost:8080/api/tasks/search?title=setup"
```

## Data Models

### Task Status
- `PENDING` - Task is created but not started
- `IN_PROGRESS` - Task is being worked on
- `COMPLETED` - Task is finished
- `CANCELLED` - Task is cancelled
- `ON_HOLD` - Task is temporarily paused

### Task Priority
- `LOW` (Level 1)
- `MEDIUM` (Level 2)
- `HIGH` (Level 3)
- `URGENT` (Level 4)
- `CRITICAL` (Level 5)

## Configuration

### Application Profiles

- **Default**: Uses MySQL configuration
- **dev**: Development profile with detailed logging
- **test**: Uses H2 in-memory database

### Environment Variables

You can override database configuration using environment variables:

```bash
export SPRING_DATASOURCE_URL=************************************
export SPRING_DATASOURCE_USERNAME=your_username
export SPRING_DATASOURCE_PASSWORD=your_password
```

## Testing

```bash
# Run all tests
mvn test

# Run with test profile
mvn test -Dspring.profiles.active=test
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Ensure MySQL is running
   - Check credentials in `application.properties`
   - Verify database `tasks_db` exists

2. **Port Already in Use**
   - Change port in `application.properties`: `server.port=8081`
   - Or kill process using port 8080

3. **Validation Errors**
   - Ensure all required fields are provided
   - Check field length limits
   - Verify enum values are correct

## Future Enhancements

- [ ] User authentication and authorization
- [ ] Task comments and attachments
- [ ] Email notifications
- [ ] Task templates
- [ ] Reporting and analytics
- [ ] Mobile app support
- [ ] Integration with external calendars

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
