# Test Profile Configuration
spring.application.name=tasks-test

# H2 In-Memory Database for Testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# JPA Configuration for Testing
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# H2 Console for Testing
spring.h2.console.enabled=true

# Logging Configuration for Testing
logging.level.com.tasks.tasks=INFO
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN

# Server Configuration for Testing
server.port=8081
