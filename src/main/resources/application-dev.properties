# Development Profile Configuration
spring.application.name=tasks

# MySQL Database Configuration for Development
spring.datasource.url=*******************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration for Development
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Logging Configuration
logging.level.com.tasks.tasks=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Enable all actuator endpoints for development
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
