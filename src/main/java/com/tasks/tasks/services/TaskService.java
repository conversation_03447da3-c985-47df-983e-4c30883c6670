package com.tasks.tasks.services;

import com.tasks.tasks.entities.Task;
import com.tasks.tasks.entities.TaskPriority;
import com.tasks.tasks.entities.TaskStatus;
import com.tasks.tasks.repositories.TaskRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
public class TaskService {
    
    private final TaskRepository taskRepository;
    
    // Create a new task
    public Task createTask(Task task) {
        return taskRepository.save(task);
    }
    
    // Get all tasks
    public List<Task> getAllTasks() {
        return taskRepository.findAll();
    }
    
    // Get task by ID
    public Optional<Task> getTaskById(Long id) {
        return taskRepository.findById(id);
    }
    
    // Update task
    public Task updateTask(Long id, Task updatedTask) {
        return taskRepository.findById(id)
                .map(task -> {
                    task.setTitle(updatedTask.getTitle());
                    task.setDescription(updatedTask.getDescription());
                    task.setStatus(updatedTask.getStatus());
                    task.setPriority(updatedTask.getPriority());
                    task.setDueDate(updatedTask.getDueDate());
                    task.setAssignedTo(updatedTask.getAssignedTo());
                    
                    // If marking as completed, set completion time
                    if (updatedTask.getStatus() == TaskStatus.COMPLETED && 
                        task.getStatus() != TaskStatus.COMPLETED) {
                        task.markAsCompleted();
                    } else if (updatedTask.getStatus() == TaskStatus.IN_PROGRESS) {
                        task.markAsInProgress();
                    }
                    
                    return taskRepository.save(task);
                })
                .orElseThrow(() -> new RuntimeException("Task not found with id: " + id));
    }
    
    // Delete task
    public void deleteTask(Long id) {
        if (!taskRepository.existsById(id)) {
            throw new RuntimeException("Task not found with id: " + id);
        }
        taskRepository.deleteById(id);
    }
    
    // Get tasks by status
    public List<Task> getTasksByStatus(TaskStatus status) {
        return taskRepository.findByStatus(status);
    }
    
    // Get tasks by priority
    public List<Task> getTasksByPriority(TaskPriority priority) {
        return taskRepository.findByPriority(priority);
    }
    
    // Get tasks assigned to a person
    public List<Task> getTasksAssignedTo(String assignedTo) {
        return taskRepository.findByAssignedTo(assignedTo);
    }
    
    // Get tasks created by a person
    public List<Task> getTasksCreatedBy(String createdBy) {
        return taskRepository.findByCreatedBy(createdBy);
    }
    
    // Search tasks by title
    public List<Task> searchTasksByTitle(String title) {
        return taskRepository.findByTitleContainingIgnoreCase(title);
    }
    
    // Get overdue tasks
    public List<Task> getOverdueTasks() {
        return taskRepository.findOverdueTasks(LocalDateTime.now(), TaskStatus.COMPLETED);
    }
    
    // Get high priority pending tasks
    public List<Task> getHighPriorityPendingTasks() {
        List<TaskPriority> highPriorities = List.of(TaskPriority.HIGH, TaskPriority.URGENT, TaskPriority.CRITICAL);
        return taskRepository.findHighPriorityPendingTasks(highPriorities, TaskStatus.PENDING);
    }
    
    // Mark task as completed
    public Task markTaskAsCompleted(Long id) {
        return taskRepository.findById(id)
                .map(task -> {
                    task.markAsCompleted();
                    return taskRepository.save(task);
                })
                .orElseThrow(() -> new RuntimeException("Task not found with id: " + id));
    }
    
    // Mark task as in progress
    public Task markTaskAsInProgress(Long id) {
        return taskRepository.findById(id)
                .map(task -> {
                    task.markAsInProgress();
                    return taskRepository.save(task);
                })
                .orElseThrow(() -> new RuntimeException("Task not found with id: " + id));
    }
    
    // Get task statistics
    public TaskStatistics getTaskStatistics() {
        long totalTasks = taskRepository.count();
        long pendingTasks = taskRepository.countByStatus(TaskStatus.PENDING);
        long inProgressTasks = taskRepository.countByStatus(TaskStatus.IN_PROGRESS);
        long completedTasks = taskRepository.countByStatus(TaskStatus.COMPLETED);
        long overdueTasks = getOverdueTasks().size();
        
        return new TaskStatistics(totalTasks, pendingTasks, inProgressTasks, completedTasks, overdueTasks);
    }
    
    // Inner class for task statistics
    public static class TaskStatistics {
        private final long totalTasks;
        private final long pendingTasks;
        private final long inProgressTasks;
        private final long completedTasks;
        private final long overdueTasks;
        
        public TaskStatistics(long totalTasks, long pendingTasks, long inProgressTasks, 
                            long completedTasks, long overdueTasks) {
            this.totalTasks = totalTasks;
            this.pendingTasks = pendingTasks;
            this.inProgressTasks = inProgressTasks;
            this.completedTasks = completedTasks;
            this.overdueTasks = overdueTasks;
        }
        
        // Getters
        public long getTotalTasks() { return totalTasks; }
        public long getPendingTasks() { return pendingTasks; }
        public long getInProgressTasks() { return inProgressTasks; }
        public long getCompletedTasks() { return completedTasks; }
        public long getOverdueTasks() { return overdueTasks; }
    }
}
