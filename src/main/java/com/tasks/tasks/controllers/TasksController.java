
package com.tasks.tasks.controllers;

import com.tasks.tasks.dto.TaskCreateRequest;
import com.tasks.tasks.dto.TaskResponse;
import com.tasks.tasks.dto.TaskUpdateRequest;
import com.tasks.tasks.entities.Task;
import com.tasks.tasks.entities.TaskPriority;
import com.tasks.tasks.entities.TaskStatus;
import com.tasks.tasks.services.TaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/tasks")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class TasksController {

    private final TaskService taskService;

    // Welcome endpoint
    @GetMapping("/")
    public ResponseEntity<String> welcome() {
        return ResponseEntity.ok("Welcome to Tasks Management API!");
    }

    // Create a new task
    @PostMapping
    public ResponseEntity<TaskResponse> createTask(@Valid @RequestBody TaskCreateRequest request) {
        Task task = new Task(
            request.getTitle(),
            request.getDescription(),
            request.getPriority(),
            request.getCreatedBy()
        );
        task.setDueDate(request.getDueDate());
        task.setAssignedTo(request.getAssignedTo());

        Task savedTask = taskService.createTask(task);
        return ResponseEntity.status(HttpStatus.CREATED).body(TaskResponse.fromTask(savedTask));
    }

    // Get all tasks
    @GetMapping
    public ResponseEntity<List<TaskResponse>> getAllTasks() {
        List<Task> tasks = taskService.getAllTasks();
        List<TaskResponse> taskResponses = tasks.stream()
                .map(TaskResponse::fromTask)
                .collect(Collectors.toList());
        return ResponseEntity.ok(taskResponses);
    }

    // Get task by ID
    @GetMapping("/{id}")
    public ResponseEntity<TaskResponse> getTaskById(@PathVariable Long id) {
        return taskService.getTaskById(id)
                .map(task -> ResponseEntity.ok(TaskResponse.fromTask(task)))
                .orElse(ResponseEntity.notFound().build());
    }

    // Update task
    @PutMapping("/{id}")
    public ResponseEntity<TaskResponse> updateTask(@PathVariable Long id,
                                                  @Valid @RequestBody TaskUpdateRequest request) {
        try {
            Task updatedTask = new Task();
            updatedTask.setTitle(request.getTitle());
            updatedTask.setDescription(request.getDescription());
            updatedTask.setStatus(request.getStatus());
            updatedTask.setPriority(request.getPriority());
            updatedTask.setDueDate(request.getDueDate());
            updatedTask.setAssignedTo(request.getAssignedTo());

            Task savedTask = taskService.updateTask(id, updatedTask);
            return ResponseEntity.ok(TaskResponse.fromTask(savedTask));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    // Delete task
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTask(@PathVariable Long id) {
        try {
            taskService.deleteTask(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    // Get tasks by status
    @GetMapping("/status/{status}")
    public ResponseEntity<List<TaskResponse>> getTasksByStatus(@PathVariable TaskStatus status) {
        List<Task> tasks = taskService.getTasksByStatus(status);
        List<TaskResponse> taskResponses = tasks.stream()
                .map(TaskResponse::fromTask)
                .collect(Collectors.toList());
        return ResponseEntity.ok(taskResponses);
    }

    // Get tasks by priority
    @GetMapping("/priority/{priority}")
    public ResponseEntity<List<TaskResponse>> getTasksByPriority(@PathVariable TaskPriority priority) {
        List<Task> tasks = taskService.getTasksByPriority(priority);
        List<TaskResponse> taskResponses = tasks.stream()
                .map(TaskResponse::fromTask)
                .collect(Collectors.toList());
        return ResponseEntity.ok(taskResponses);
    }

    // Get tasks assigned to a person
    @GetMapping("/assigned/{assignedTo}")
    public ResponseEntity<List<TaskResponse>> getTasksAssignedTo(@PathVariable String assignedTo) {
        List<Task> tasks = taskService.getTasksAssignedTo(assignedTo);
        List<TaskResponse> taskResponses = tasks.stream()
                .map(TaskResponse::fromTask)
                .collect(Collectors.toList());
        return ResponseEntity.ok(taskResponses);
    }

    // Search tasks by title
    @GetMapping("/search")
    public ResponseEntity<List<TaskResponse>> searchTasks(@RequestParam String title) {
        List<Task> tasks = taskService.searchTasksByTitle(title);
        List<TaskResponse> taskResponses = tasks.stream()
                .map(TaskResponse::fromTask)
                .collect(Collectors.toList());
        return ResponseEntity.ok(taskResponses);
    }

    // Get overdue tasks
    @GetMapping("/overdue")
    public ResponseEntity<List<TaskResponse>> getOverdueTasks() {
        List<Task> tasks = taskService.getOverdueTasks();
        List<TaskResponse> taskResponses = tasks.stream()
                .map(TaskResponse::fromTask)
                .collect(Collectors.toList());
        return ResponseEntity.ok(taskResponses);
    }

    // Get high priority pending tasks
    @GetMapping("/high-priority-pending")
    public ResponseEntity<List<TaskResponse>> getHighPriorityPendingTasks() {
        List<Task> tasks = taskService.getHighPriorityPendingTasks();
        List<TaskResponse> taskResponses = tasks.stream()
                .map(TaskResponse::fromTask)
                .collect(Collectors.toList());
        return ResponseEntity.ok(taskResponses);
    }

    // Mark task as completed
    @PatchMapping("/{id}/complete")
    public ResponseEntity<TaskResponse> markTaskAsCompleted(@PathVariable Long id) {
        try {
            Task task = taskService.markTaskAsCompleted(id);
            return ResponseEntity.ok(TaskResponse.fromTask(task));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    // Mark task as in progress
    @PatchMapping("/{id}/in-progress")
    public ResponseEntity<TaskResponse> markTaskAsInProgress(@PathVariable Long id) {
        try {
            Task task = taskService.markTaskAsInProgress(id);
            return ResponseEntity.ok(TaskResponse.fromTask(task));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    // Get task statistics
    @GetMapping("/statistics")
    public ResponseEntity<TaskService.TaskStatistics> getTaskStatistics() {
        TaskService.TaskStatistics stats = taskService.getTaskStatistics();
        return ResponseEntity.ok(stats);
    }
}
