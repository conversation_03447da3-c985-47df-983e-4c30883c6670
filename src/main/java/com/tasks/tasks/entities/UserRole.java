package com.tasks.tasks.entities;

public enum UserRole {
    <PERSON><PERSON><PERSON>("Administrator"),
    MANAGER("Manager"),
    USER("User"),
    GUEST("Guest");
    
    private final String displayName;
    
    UserRole(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
