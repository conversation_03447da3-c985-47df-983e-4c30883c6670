package com.tasks.tasks.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "tasks")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Task {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 255)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TaskStatus status = TaskStatus.PENDING;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TaskPriority priority = TaskPriority.MEDIUM;
    
    @Column(name = "due_date")
    private LocalDateTime dueDate;
    
    @Column(name = "assigned_to")
    private String assignedTo;

    @Column(name = "created_by")
    private String createdBy;

    // Optional: Add relationships to User entity (uncomment when User management is needed)
    // @ManyToOne(fetch = FetchType.LAZY)
    // @JoinColumn(name = "assigned_to", referencedColumnName = "username", insertable = false, updatable = false)
    // private User assignedUser;

    // @ManyToOne(fetch = FetchType.LAZY)
    // @JoinColumn(name = "created_by", referencedColumnName = "username", insertable = false, updatable = false)
    // private User createdUser;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    // Custom constructor for creating new tasks
    public Task(String title, String description, TaskPriority priority, String createdBy) {
        this.title = title;
        this.description = description;
        this.priority = priority;
        this.createdBy = createdBy;
        this.status = TaskStatus.PENDING;
    }
    
    // Helper method to mark task as completed
    public void markAsCompleted() {
        this.status = TaskStatus.COMPLETED;
        this.completedAt = LocalDateTime.now();
    }
    
    // Helper method to mark task as in progress
    public void markAsInProgress() {
        this.status = TaskStatus.IN_PROGRESS;
        this.completedAt = null;
    }
}
