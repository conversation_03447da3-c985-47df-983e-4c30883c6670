package com.tasks.tasks.repositories;

import com.tasks.tasks.entities.Task;
import com.tasks.tasks.entities.TaskPriority;
import com.tasks.tasks.entities.TaskStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TaskRepository extends JpaRepository<Task, Long> {
    
    // Find tasks by status
    List<Task> findByStatus(TaskStatus status);
    
    // Find tasks by priority
    List<Task> findByPriority(TaskPriority priority);
    
    // Find tasks assigned to a specific person
    List<Task> findByAssignedTo(String assignedTo);
    
    // Find tasks created by a specific person
    List<Task> findByCreatedBy(String createdBy);
    
    // Find tasks by title containing (case insensitive)
    List<Task> findByTitleContainingIgnoreCase(String title);
    
    // Find tasks due before a certain date
    List<Task> findByDueDateBefore(LocalDateTime dueDate);
    
    // Find tasks due after a certain date
    List<Task> findByDueDateAfter(LocalDateTime dueDate);
    
    // Find overdue tasks (due date passed and not completed)
    @Query("SELECT t FROM Task t WHERE t.dueDate < :currentDate AND t.status != :completedStatus")
    List<Task> findOverdueTasks(@Param("currentDate") LocalDateTime currentDate, 
                               @Param("completedStatus") TaskStatus completedStatus);
    
    // Find tasks by status and assigned to
    List<Task> findByStatusAndAssignedTo(TaskStatus status, String assignedTo);
    
    // Find tasks by priority and status
    List<Task> findByPriorityAndStatus(TaskPriority priority, TaskStatus status);
    
    // Count tasks by status
    long countByStatus(TaskStatus status);
    
    // Count tasks by assigned person
    long countByAssignedTo(String assignedTo);
    
    // Find tasks created between dates
    List<Task> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    // Custom query to find high priority pending tasks
    @Query("SELECT t FROM Task t WHERE t.priority IN (:priorities) AND t.status = :status ORDER BY t.priority DESC, t.createdAt ASC")
    List<Task> findHighPriorityPendingTasks(@Param("priorities") List<TaskPriority> priorities, 
                                           @Param("status") TaskStatus status);
}
