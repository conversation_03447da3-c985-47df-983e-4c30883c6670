-- MySQL Setup Script for Tasks Management System
-- Run this script to set up the database and user

-- Create database
CREATE DATABASE IF NOT EXISTS tasks_db;

-- Create user (optional - you can use root)
CREATE USER IF NOT EXISTS 'tasks_user'@'localhost' IDENTIFIED BY 'tasks_password';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON tasks_db.* TO 'tasks_user'@'localhost';
GRANT ALL PRIVILEGES ON tasks_db.* TO 'root'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Use the database
USE tasks_db;

-- Show tables (will be empty initially, <PERSON>bernate will create them)
SHOW TABLES;

-- Optional: Create some sample data after running the application
-- INSERT INTO users (username, email, first_name, last_name, role, active, created_at, updated_at) 
-- VALUES 
-- ('john_doe', '<EMAIL>', 'John', 'Doe', 'USER', true, NOW(), NOW()),
-- ('jane_smith', '<EMAIL>', '<PERSON>', '<PERSON>', 'MANAGER', true, NOW(), NOW()),
-- ('admin_user', '<EMAIL>', 'Admin', 'User', 'ADMIN', true, NOW(), NOW());

-- INSERT INTO tasks (title, description, status, priority, assigned_to, created_by, created_at, updated_at)
-- VALUES 
-- ('Setup Development Environment', 'Install and configure all necessary development tools', 'PENDING', 'HIGH', 'john_doe', 'jane_smith', NOW(), NOW()),
-- ('Create Database Schema', 'Design and implement the database schema for the application', 'IN_PROGRESS', 'URGENT', 'jane_smith', 'admin_user', NOW(), NOW()),
-- ('Write Unit Tests', 'Create comprehensive unit tests for all service methods', 'PENDING', 'MEDIUM', 'john_doe', 'jane_smith', NOW(), NOW()),
-- ('Deploy to Production', 'Deploy the application to production environment', 'PENDING', 'CRITICAL', 'admin_user', 'admin_user', DATE_ADD(NOW(), INTERVAL 7 DAY), NOW());
